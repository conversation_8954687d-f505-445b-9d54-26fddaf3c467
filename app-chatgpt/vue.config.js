const { defineConfig } = require('@vue/cli-service');
const path = require('path');
const devServerConfig = require('./config/devServer');

module.exports = defineConfig({
    transpileDependencies: true,
    configureWebpack: {
        resolve: {
            alias: {
                '@': path.resolve(__dirname, 'src'),
            },
        },
        devServer: {
            ...devServerConfig.getDevServerConfig(),
            proxy: {},
        },
        output: {
            libraryTarget: 'system',
        },
        externals: [/^@dealer\/.+/],
        plugins: [
            require('unplugin-vue-components/webpack').default({
                /* options */
            }),
            require('unplugin-auto-import/webpack').default({
                /* options */
            }),
        ],
        devtool: process.env.NODE_ENV === 'production' ? false : 'source-map',
    },
    productionSourceMap: false, // 保留这一行
});
